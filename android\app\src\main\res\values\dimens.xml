<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--
Refer to App Widget Documentation for margin information
http://developer.android.com/guide/topics/appwidgets/index.html#CreatingLayout
    -->
    <dimen name="widget_margin">0dp</dimen>

    <!-- Widget dimensions -->
    <dimen name="widget_padding">14dp</dimen>
    <dimen name="widget_corner_radius">16dp</dimen>

    <!-- Header dimensions -->
    <dimen name="header_margin_bottom">10dp</dimen>
    <dimen name="title_text_size">15sp</dimen>
    <dimen name="button_size">30dp</dimen>
    <dimen name="button_margin">8dp</dimen>

    <!-- Objective item dimensions -->
    <dimen name="objective_item_padding_vertical">6dp</dimen>
    <dimen name="objective_item_padding_horizontal">10dp</dimen>
    <dimen name="objective_item_margin_bottom">4dp</dimen>
    <dimen name="objective_checkbox_size">22dp</dimen>
    <dimen name="objective_checkbox_margin">10dp</dimen>

    <!-- Text sizes -->
    <dimen name="objective_title_text_size">13sp</dimen>
    <dimen name="objective_streak_text_size">11sp</dimen>
    <dimen name="empty_message_text_size">13sp</dimen>

    <!-- Spacing -->
    <dimen name="small_spacing">4dp</dimen>
    <dimen name="medium_spacing">8dp</dimen>
    <dimen name="large_spacing">16dp</dimen>

</resources>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="@dimen/widget_padding"
    android:background="@drawable/widget_background_modern">

    <!-- Header with title and add button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="@dimen/header_margin_bottom">

        <TextView
            android:id="@+id/widget_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Today's Objectives"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/title_text_size"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:fontFamily="@font/ubuntu_medium" />

        <ImageButton
            android:id="@+id/add_objective_button"
            android:layout_width="@dimen/button_size"
            android:layout_height="@dimen/button_size"
            android:src="@drawable/ic_add_thick"
            android:background="@drawable/add_button_background"
            android:tint="#FFFFFF"
            android:contentDescription="Add New Objective"
            android:scaleType="centerInside"
            android:padding="6dp"
            android:layout_marginStart="@dimen/button_margin" />

    </LinearLayout>

    <!-- Objectives container -->
    <LinearLayout
        android:id="@+id/objectives_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="visible" />

    <!-- Empty state message -->
    <TextView
        android:id="@+id/empty_message"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="Tap + to add your first objective"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/empty_message_text_size"
        android:gravity="center"
        android:visibility="gone"
        android:fontFamily="@font/ubuntu_regular"
        android:lineSpacingExtra="4dp" />

</LinearLayout>

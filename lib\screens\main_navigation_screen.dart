import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/widget_service.dart';
import '../providers/objectives_provider.dart';
import 'home_screen.dart';
import 'analytics_screen.dart';
import 'settings_screen.dart';
import 'add_objective_screen.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> with WidgetsBindingObserver {
  int _currentIndex = 0;
  late PageController _pageController;

  final List<Widget> _screens = [
    const HomeScreen(),
    const AnalyticsScreen(),
    const SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    WidgetsBinding.instance.addObserver(this);

    // Set up widget service callbacks
    _setupWidgetCallbacks();
  }
  
  void _setupWidgetCallbacks() {
    // Handle navigation to add objective screen from widget
    WidgetService.setNavigateToAddObjectiveCallback(() {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const AddObjectiveScreen(),
        ),
      );
    });

    // Handle objective completion from widget
    WidgetService.setCompleteObjectiveCallback((int objectiveId) async {
      final provider = Provider.of<ObjectivesProvider>(context, listen: false);
      await provider.markObjectiveComplete(objectiveId);
    });

    // Trigger widget update when app starts and start periodic sync
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<ObjectivesProvider>(context, listen: false);
      if (provider.objectives.isNotEmpty) {
        WidgetService.updateTodayObjectivesWidget(objectives: provider.objectives);
      }
      // Start periodic sync to detect widget changes
      WidgetService.startPeriodicSync();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _pageController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        // App became active, start sync and check immediately
        WidgetService.startPeriodicSync();
        WidgetService.syncWidgetCompletions();
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        // App going to background, stop periodic sync to save battery
        WidgetService.stopPeriodicSync();
        break;
      case AppLifecycleState.detached:
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  void _onTabTapped(int index) {
    if (_currentIndex != index) {
      setState(() {
        _currentIndex = index;
      });
      _pageController.jumpToPage(index);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Scaffold(
      body: isLandscape 
        ? _buildLandscapeLayout()
        : _buildPortraitLayout(),
    );
  }
  
  Widget _buildLandscapeLayout() {
    return Row(
      children: [
        // Navigation rail on the left side
        NavigationRail(
          selectedIndex: _currentIndex,
          onDestinationSelected: _onTabTapped,
          labelType: NavigationRailLabelType.selected,
          useIndicator: true,
          destinations: [
            NavigationRailDestination(
              icon: const Icon(Icons.home),
              selectedIcon: Icon(
                Icons.home,
                color: Theme.of(context).colorScheme.primary,
              ),
              label: const Text('Home'),
            ),
            NavigationRailDestination(
              icon: Image.asset(
                'assets/analytics.png',
                width: 24,
                height: 24,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              selectedIcon: Image.asset(
                'assets/analytics.png',
                width: 24,
                height: 24,
                color: Theme.of(context).colorScheme.primary,
              ),
              label: const Text('Analytics'),
            ),
            NavigationRailDestination(
              icon: const Icon(Icons.settings),
              selectedIcon: Icon(
                Icons.settings,
                color: Theme.of(context).colorScheme.primary,
              ),
              label: const Text('Settings'),
            ),
          ],
        ),
        
        // Content area (takes the remaining space)
        Expanded(
          child: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            children: _screens,
          ),
        ),
      ],
    );
  }
  
  Widget _buildPortraitLayout() {
    return Column(
      children: [
        // Main content area
        Expanded(
          child: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            children: _screens,
          ),
        ),
        
        // Bottom navigation bar
        BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: _onTabTapped,
          type: BottomNavigationBarType.fixed,
          elevation: 8,
          selectedItemColor: Theme.of(context).colorScheme.primary,
          unselectedItemColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 11,
          ),
          items: [
            BottomNavigationBarItem(
              icon: Container(
                padding: EdgeInsets.all(_currentIndex == 0 ? 8 : 4),
                decoration: BoxDecoration(
                  color: _currentIndex == 0
                      ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.home,
                  size: _currentIndex == 0 ? 26 : 24,
                ),
              ),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Container(
                padding: EdgeInsets.all(_currentIndex == 1 ? 8 : 4),
                decoration: BoxDecoration(
                  color: _currentIndex == 1
                      ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Image.asset(
                  'assets/analytics.png',
                  width: _currentIndex == 1 ? 26 : 24,
                  height: _currentIndex == 1 ? 26 : 24,
                  color: _currentIndex == 1
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              label: 'Analytics',
            ),
            BottomNavigationBarItem(
              icon: Container(
                padding: EdgeInsets.all(_currentIndex == 2 ? 8 : 4),
                decoration: BoxDecoration(
                  color: _currentIndex == 2
                      ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.settings,
                  size: _currentIndex == 2 ? 26 : 24,
                ),
              ),
              label: 'Settings',
            ),
          ],
        ),
      ],
    );
  }
}
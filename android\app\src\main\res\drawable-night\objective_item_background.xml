<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#1A237E" />
            <corners android:radius="8dp" />
        </shape>
    </item>

    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/objective_background" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>

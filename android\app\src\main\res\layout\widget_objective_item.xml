<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingVertical="@dimen/objective_item_padding_vertical"
    android:paddingHorizontal="@dimen/objective_item_padding_horizontal"
    android:background="@drawable/objective_item_background"
    android:layout_marginBottom="@dimen/objective_item_margin_bottom">

    <!-- Checkbox -->
    <ImageView
        android:id="@+id/objective_checkbox"
        android:layout_width="@dimen/objective_checkbox_size"
        android:layout_height="@dimen/objective_checkbox_size"
        android:src="@drawable/ic_circle_outline"
        android:tint="@color/checkbox_uncompleted"
        android:contentDescription="Complete objective"
        android:layout_marginEnd="@dimen/objective_checkbox_margin"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:padding="3dp" />

    <!-- Objective content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Objective title -->
        <TextView
            android:id="@+id/objective_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Objective Title"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/objective_title_text_size"
            android:textStyle="normal"
            android:maxLines="1"
            android:ellipsize="end"
            android:lineSpacingExtra="0dp"
            android:fontFamily="sans-serif" />

        <!-- Streak info -->
        <TextView
            android:id="@+id/objective_streak"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="5 day streak"
            android:textColor="@color/text_success"
            android:textSize="@dimen/objective_streak_text_size"
            android:textStyle="bold"
            android:visibility="gone"
            android:layout_marginTop="2dp"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

</LinearLayout>

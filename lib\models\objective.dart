import 'package:intl/intl.dart';

class Objective {
  int? id;
  String title;
  String description;
  DateTime startDate;
  DateTime? endDate;
  bool hasReminder;
  CustomTimeOfDay? reminderTime; // Changed from TimeOfDay
  int currentStreak;
  int longestStreak;
  int displayOrder;
  int color; // Color value stored as int
  List<DateTime> completedDays;
  List<Reflection> reflections;

  Objective({
    this.id,
    required this.title,
    this.description = '',
    required this.startDate,
    this.endDate,
    this.hasReminder = false,
    this.reminderTime, // Changed from TimeOfDay
    this.currentStreak = 0,
    this.longestStreak = 0,
    this.displayOrder = 0,
    this.color = 0xFF2196F3, // Default blue color
    List<DateTime>? completedDays,
    List<Reflection>? reflections,
  }) : 
    this.completedDays = completedDays ?? [],
    this.reflections = reflections ?? [];

  // Convert to and from Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'hasReminder': hasReminder ? 1 : 0,
      'reminderTime': reminderTime != null 
          ? '${reminderTime!.hour}:${reminderTime!.minute}' 
          : null,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'displayOrder': displayOrder,
      'color': color,
      'completedDays': completedDays.map((date) => date.toIso8601String()).join(','),
    };
  }

  factory Objective.fromMap(Map<String, dynamic> map) {
    CustomTimeOfDay? reminderTime; // Changed from TimeOfDay
    if (map['reminderTime'] != null) {
      final parts = map['reminderTime'].split(':');
      reminderTime = CustomTimeOfDay( // Changed from TimeOfDay
        hour: int.parse(parts[0]), 
        minute: int.parse(parts[1])
      );
    }

    List<DateTime> completedDays = [];
    if (map['completedDays'] != null && map['completedDays'].isNotEmpty) {
      completedDays = map['completedDays']
          .split(',')
          .map<DateTime>((dateStr) => DateTime.parse(dateStr))
          .toList();
    }

    return Objective(
      id: map['id'],
      title: map['title'],
      description: map['description'] ?? '',
      startDate: DateTime.parse(map['startDate']),
      endDate: map['endDate'] != null ? DateTime.parse(map['endDate']) : null,
      hasReminder: map['hasReminder'] == 1,
      reminderTime: reminderTime, // Changed from TimeOfDay
      currentStreak: map['currentStreak'] ?? 0,
      longestStreak: map['longestStreak'] ?? 0,
      displayOrder: map['displayOrder'] ?? 0,
      color: map['color'] ?? 0xFF2196F3, // Default blue if not set
      completedDays: completedDays,
    );
  }

  // Methods to manage streaks
  bool isCompleteForToday() {
    final today = DateTime.now();
    return completedDays.any((date) => 
      date.year == today.year && 
      date.month == today.month && 
      date.day == today.day
    );
  }

  void markAsComplete() {
    final today = DateTime.now();

    // Check if already completed today
    if (isCompleteForToday()) return;

    completedDays.add(today);

    // Recalculate both current streak and longest streak
    _recalculateCurrentStreak();
    _recalculateLongestStreak();
  }

  void unmarkAsComplete() {
    final today = DateTime.now();

    // Remove today's completion if it exists
    completedDays.removeWhere((date) =>
      date.year == today.year &&
      date.month == today.month &&
      date.day == today.day
    );

    // Recalculate both current streak and longest streak
    _recalculateCurrentStreak();
    _recalculateLongestStreak();
  }

  void _recalculateCurrentStreak() {
    if (completedDays.isEmpty) {
      currentStreak = 0;
      return;
    }

    // Sort completed days in descending order
    final sortedDays = List<DateTime>.from(completedDays)
      ..sort((a, b) => b.compareTo(a));

    currentStreak = 0;
    DateTime? previousDate;

    for (final date in sortedDays) {
      if (previousDate == null) {
        // First date (most recent)
        final today = DateTime.now();
        final dateOnly = DateTime(date.year, date.month, date.day);
        final todayOnly = DateTime(today.year, today.month, today.day);

        // Check if the most recent completion is today or yesterday
        final daysDifference = todayOnly.difference(dateOnly).inDays;
        if (daysDifference <= 1) {
          currentStreak = 1;
          previousDate = dateOnly;
        } else {
          // Gap found, no current streak
          break;
        }
      } else {
        // Check if this date is consecutive with the previous date
        final dateOnly = DateTime(date.year, date.month, date.day);
        final daysDifference = previousDate.difference(dateOnly).inDays;

        if (daysDifference == 1) {
          currentStreak++;
          previousDate = dateOnly;
        } else {
          // Gap found, stop counting
          break;
        }
      }
    }
  }

  void _recalculateLongestStreak() {
    if (completedDays.isEmpty) {
      longestStreak = 0;
      return;
    }

    // Sort completed days in ascending order for chronological processing
    final sortedDays = List<DateTime>.from(completedDays)
      ..sort((a, b) => a.compareTo(b));

    int maxStreak = 0;
    int currentStreakCount = 0;
    DateTime? previousDate;

    for (final date in sortedDays) {
      final dateOnly = DateTime(date.year, date.month, date.day);

      if (previousDate == null) {
        // First date
        currentStreakCount = 1;
        previousDate = dateOnly;
      } else {
        // Check if this date is consecutive with the previous date
        final daysDifference = dateOnly.difference(previousDate).inDays;

        if (daysDifference == 1) {
          // Consecutive day, increment streak
          currentStreakCount++;
        } else {
          // Gap found, update max streak if needed and reset current streak
          maxStreak = maxStreak > currentStreakCount ? maxStreak : currentStreakCount;
          currentStreakCount = 1;
        }
        previousDate = dateOnly;
      }
    }

    // Don't forget to check the final streak
    maxStreak = maxStreak > currentStreakCount ? maxStreak : currentStreakCount;
    longestStreak = maxStreak;
  }

  void addReflection(String note) {
    reflections.add(
      Reflection(
        id: DateTime.now().millisecondsSinceEpoch,
        date: DateTime.now(),
        note: note,
      ),
    );
  }

  // Format date methods
  String get formattedStartDate => DateFormat('MMM d, yyyy').format(startDate);
  
  String get formattedEndDate => endDate != null ? DateFormat('MMM d, yyyy').format(endDate!) : '';
  
  bool get isExpired => endDate != null && DateTime.now().isAfter(endDate!);
  
  bool get isActive => !isExpired;
  
  // Completion percentage calculation for objectives with end dates
  double? get completionPercentage {
    if (endDate == null) return null;
    
    final totalDays = endDate!.difference(startDate).inDays + 1;
    
    // Calculate percentage based on completed days vs total allocated days
    final completedDaysCount = completedDays.where((date) => 
      date.isAfter(startDate.subtract(const Duration(days: 1))) && 
      date.isBefore(endDate!.add(const Duration(days: 1)))
    ).length;
    
    return (completedDaysCount / totalDays * 100).clamp(0.0, 100.0);
  }
  
  String get streakText => 
      currentStreak > 0 ? '$currentStreak day${currentStreak > 1 ? "s" : ""}' : 'No streak yet';
      
  String get longestStreakText => 
      longestStreak > 0 ? '$longestStreak day${longestStreak > 1 ? "s" : ""}' : 'No streak yet';
}

class Reflection {
  int? id;
  DateTime date;
  String note;

  Reflection({
    this.id,
    required this.date,
    required this.note,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'note': note,
    };
  }

  factory Reflection.fromMap(Map<String, dynamic> map) {
    return Reflection(
      id: map['id'],
      date: DateTime.parse(map['date']),
      note: map['note'],
    );
  }

  String get formattedDate => DateFormat('MMM d, yyyy').format(date);
}

// Helper class for TimeOfDay serialization
class CustomTimeOfDay { // Renamed from TimeOfDay
  final int hour;
  final int minute;

  CustomTimeOfDay({required this.hour, required this.minute}); // Renamed from TimeOfDay

  @override
  String toString() {
    String hourStr = hour.toString().padLeft(2, '0');
    String minuteStr = minute.toString().padLeft(2, '0');
    return '$hourStr:$minuteStr';
  }
}